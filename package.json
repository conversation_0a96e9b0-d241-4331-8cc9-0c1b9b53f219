{"name": "backend", "version": "1.0.0", "description": "", "license": "ISC", "author": "", "type": "commonjs", "main": "index.js", "scripts": {"start": "nodemon server.js", "dev": "nodemon server.js"}, "dependencies": {"axios": "^1.9.0", "cors": "^2.8.5", "express": "^5.1.0", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "nodemon": "^3.1.10", "socket.io": "^4.8.1"}, "pkg": {"assets": ["node_modules/axios/dist/node/axios.cjs"], "scripts": "server.js", "targets": ["node22-win-x64"], "outputPath": "dist"}}