// const axios = require('axios/dist/node/axios.cjs');
const axios = require('axios');


async function request(battleId) {
  let data = JSON.stringify({
    "endpoint": "/battledata",
    "parameters": {
      "battleid": battleId
    }
  });

  let config = {
    method: 'post',
    maxBodyLength: Infinity,
    url: 'https://mlbb.splay.vn/Home/CallApi',
    data: data,
    headers: {
      'Content-Type': 'application/json'
    }
  };

  try {
    const response = await axios.request(config);
    return response.data;
  } catch (error) {
    console.error(error);
    throw error; // re-throw if needed
  }
}

module.exports = request;
