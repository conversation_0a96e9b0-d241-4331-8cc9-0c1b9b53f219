const express = require('express');
const http = require('http');
const socketIO = require('socket.io');

const cors = require('cors');
const bodyParser = require('body-parser');
const jwt = require('jsonwebtoken'); // nên để .env
const request = require('./request');


// Cấu hình server
const app = express();
const server = http.createServer(app);
const io = socketIO(server, {
  cors: {
    origin: "*", // Allow all origins. Replace with specific URL in production
    methods: ["GET", "POST"]
  }
});
// Thông tin đăng nhập admin
const ADMIN_USER = 'admin';
const ADMIN_PASSWORD = 'admin123';

const JWT_SECRET = 'your_jwt_secret_key'; // nên để .env, không để mặc định như này

app.use(cors({
  origin: '*', // domain React app
}));

app.use(express.urlencoded({ extended: true }));
app.use(express.json());

// Lưu trữ các client kết nối
const clients = new Map();

app.use(bodyParser.json());


// Route login
app.post('/login', (req, res) => {
  const { username, password } = req.body;

  if (username === ADMIN_USER && password === ADMIN_PASSWORD) {
    // Tạo JWT token
    const token = jwt.sign({ username, isAdmin: true }, JWT_SECRET, {
      expiresIn: '1h'
    });

    res.json({ token });
  } else {
    res.status(401).json({ message: 'Invalid credentials' });
  }
});

// Protected route
app.get('/admin', (req, res) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.sendStatus(401);
  }

  const token = authHeader.split(' ')[1];

  try {
    const decoded = jwt.verify(token, JWT_SECRET);

    if (decoded.isAdmin) {
      res.json({ message: `Welcome admin ${decoded.username}` });
    } else {
      res.sendStatus(403);
    }
  } catch (err) {
    res.sendStatus(403);
  }
});

// Socket.IO
io.on('connection', async (socket) => {
  console.log('Client connected:', socket.id);
  const token = socket.handshake.auth.token;

  if (token != "client" || !token) {

    const decoded = jwt.verify(token, JWT_SECRET);

    if (decoded.isAdmin) {
      clients.set(socket.id, {
        id: socket.id,
        isAdmin: true
      });

      console.log('Admin authenticated:', socket.id);
    }

  }
  updateClientList();
  // Đặt sự kiện disconnect trong phạm vi của socket
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
    clients.delete(socket.id);
    updateClientList();
  });

  // Nhận lệnh từ admin và gửi đến tất cả client
  socket.on('admin-command', async (command) => {
    const client = clients.get(socket.id);
    if (client && client.isAdmin) {
      console.log('Admin command:', command);
      // Gửi lệnh đến tất cả client (trừ admin)
      const data = await request('925289180264852488');
      const json2 = {
        city: "Hanoi",
        country: "Vietnam"
      };
      const merged = Object.assign({}, data, json2);
      socket.broadcast.emit('command', merged);
    }
  });

  // Xử lý ngắt kết nối
  socket.on('disconnect', async () => {
    console.log('Client disconnected:', socket.id);
    clients.delete(socket.id);
    const sockets = await io.fetchSockets();
    const clientList = sockets
      .filter(s => {
        const info = clients.get(s.id);
        return !(info && info.isAdmin); // bỏ qua nếu là admin
      })
      .map(s => ({
        id: s.id,
        handshake: s.handshake,
        rooms: Array.from(s.rooms),
      }));

    sockets.forEach(s => {
      const info = clients.get(s.id);
      if (info && info.isAdmin) {
        s.emit('client-list', clientList);
      }
    });
  });

});

function updateClientList() {
  const sockets = io.sockets.sockets;
  const clientList = Array.from(sockets.values())
    .filter(s => {
      const info = clients.get(s.id);
      return !(info && info.isAdmin); // bỏ qua nếu là admin
    })
    .map(s => ({
      id: s.id,
      handshake: s.handshake,
      rooms: Array.from(s.rooms),
    }));

  sockets.forEach(s => {
    const info = clients.get(s.id);
    if (info && info.isAdmin) {
      s.emit('client-list', clientList);
    }
  });
}



// Khởi động server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}`);
});
